# PowerShell wrapper script to run MCP servers with correct Node.js environment
# This script isolates the Node.js environment from System32 conflicts

param(
    [Parameter(Mandatory=$true)]
    [string]$PackageName,
    
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$RemainingArgs
)

# Set explicit environment variables to use the correct Node.js installation
$env:PATH = "C:\Program Files\nodejs;C:\Program Files\nodejs\node_modules\.bin;$env:PATH"
$env:NODE_PATH = "C:\Program Files\nodejs\node_modules"

# Remove any potential System32 Node.js from PATH temporarily
$currentPath = $env:PATH
$pathEntries = $currentPath -split ';'
$filteredPath = $pathEntries | Where-Object { $_ -notlike "*System32*" }
$env:PATH = $filteredPath -join ';'

# Run the MCP server with the correct Node.js environment
$npxPath = "C:\Program Files\nodejs\npx.cmd"

# Build the argument list
$arguments = @("-y", $PackageName)
if ($RemainingArgs) {
    $arguments += $RemainingArgs
}

try {
    # Use Start-Process to avoid issues with stdio redirection
    & $npxPath @arguments
} catch {
    Write-Error "Failed to run MCP server: $_"
    exit 1
}
